"""
数据库连接管理模块。

该模块提供用于管理数据库连接和执行查询的功能。
它处理连接池、查询执行和错误处理。
"""

import mysql.connector
from mysql.connector import Error, pooling
import os
import time
from src.utils.logger import logger
from typing import Dict, Any, Optional, Union
from src.db.database_enum import Database

from dotenv import load_dotenv
load_dotenv()

# --- 数据库配置常量 ---
# 通用设置
DB_CHARSET = os.getenv("DB_CHARSET", "utf8mb4") # 使用 utf8mb4 支持表情符号
DB_EXECUTE_TIMEOUT_MS = int(os.getenv("DB_EXECUTE_TIMEOUT_MS", 300000)) # 默认 5 分钟（毫秒）

# ChatBI 数据库配置 (项目自身数据) - 统一使用MySQL
CHATBI_DB_HOST = os.getenv("CHATBI_MYSQL_HOST", "mysql-xm.summerfarm.net")
CHATBI_DB_PORT = int(os.getenv("CHATBI_MYSQL_PORT", 3308))
CHATBI_DB_USER = os.getenv("CHATBI_MYSQL_USER", "test")
CHATBI_DB_PASSWORD = os.getenv("CHATBI_MYSQL_PASSWORD", "xianmu619")
CHATBI_DB_NAME = os.getenv("CHATBI_MYSQL_DATABASE", "chatbi")
CHATBI_POOL_NAME = "chatbi_pool"
CHATBI_POOL_SIZE = int(os.getenv("CHATBI_MYSQL_POOL_SIZE", 30)) # 可配置的池大小
CHATBI_CONNECT_TIMEOUT = int(os.getenv("CHATBI_CONNECT_TIMEOUT", 10)) # 连接超时（秒）
CHATBI_POOL_GET_TIMEOUT = int(os.getenv("CHATBI_POOL_GET_TIMEOUT", 30)) # 从连接池获取连接的等待超时（秒）

# 业务数据库 (业务表，如订单、商户等)
BUSINESS_DB_HOST = os.getenv("DB_HOST", "rr-bp19zkvz53m17h8qsmo.mysql.rds.aliyuncs.com")
BUSINESS_DB_PORT = int(os.getenv("DB_PORT", 3306))
BUSINESS_DB_USER = os.getenv("DB_USER", "xianmu_ai")
BUSINESS_DB_PASSWORD = os.getenv("DB_PASSWORD", "Xianmu_ai")
BUSINESS_DB_NAME = os.getenv("DB_NAME", "xianmudb")
BUSINESS_POOL_NAME = "business_pool"
BUSINESS_POOL_SIZE = int(os.getenv("BUSINESS_MYSQL_POOL_SIZE", 30)) # 可配置的池大小
BUSINESS_CONNECT_TIMEOUT = int(os.getenv("DB_CONNECT_TIMEOUT", 3)) # 连接超时（秒）
BUSINESS_POOL_GET_TIMEOUT = int(os.getenv("BUSINESS_POOL_GET_TIMEOUT", 30)) # 从连接池获取连接的等待超时（秒）

# 逻辑数据仓库 (数据分析和报表)
LOGICAL_DW_DB_HOST = os.getenv("LOGICAL_DW_HOST", "logic-dw-pub.proxy.dms.aliyuncs.com")
LOGICAL_DW_DB_PORT = int(os.getenv("LOGICAL_DW_PORT", 3306))
LOGICAL_DW_DB_USER = os.getenv("LOGICAL_DW_USER", "SFc2SI3dUf3m4T5x0Zy15ibr")
LOGICAL_DW_DB_PASSWORD = os.getenv("LOGICAL_DW_PASSWORD", "U9ZC4trBzHCWnc2ht15j71XR7YBVpG")
LOGICAL_DW_DB_NAME = os.getenv("LOGICAL_DW_NAME", "")  # 不指定默认数据库，连接后可切换到任意数据库
LOGICAL_DW_POOL_NAME = "logical_dw_pool"
LOGICAL_DW_POOL_SIZE = int(os.getenv("LOGICAL_DW_MYSQL_POOL_SIZE", 15)) # 可配置的池大小
LOGICAL_DW_CONNECT_TIMEOUT = int(os.getenv("LOGICAL_DW_CONNECT_TIMEOUT", 5)) # 连接超时（秒）
LOGICAL_DW_POOL_GET_TIMEOUT = int(os.getenv("LOGICAL_DW_POOL_GET_TIMEOUT", 30)) # 从连接池获取连接的等待超时（秒）

# --- 全局连接池变量 ---
chatbi_db_pool: Optional[pooling.MySQLConnectionPool] = None
business_db_pool: Optional[pooling.MySQLConnectionPool] = None
logical_dw_db_pool: Optional[pooling.MySQLConnectionPool] = None

# --- 数据库连接池初始化 (模块加载时执行) ---

def _initialize_pool(pool_config: Dict[str, Any]) -> Optional[pooling.MySQLConnectionPool]:
    """内部辅助函数，用于初始化单个连接池"""
    try:
        pool_name = pool_config["pool_name"]
        db_name = pool_config["database"]
        logger.info(f"Initializing MySQL connection pool for {pool_name}: host={pool_config['host']}, port={pool_config['port']}, user={pool_config['user']}, db={db_name or 'none'}, pool_size={pool_config['pool_size']}")

        # 构建连接池配置
        pool_kwargs = {
            "pool_name": pool_name,
            "pool_reset_session": False,
            "pool_size": pool_config["pool_size"],
            "host": pool_config["host"],
            "port": pool_config["port"],
            "user": pool_config["user"],
            "password": pool_config["password"],
            "charset": DB_CHARSET,
            "auth_plugin": 'mysql_native_password',
            "autocommit": True,
            "buffered": True,  # 启用buffered模式，避免"Unread result found"错误
            "consume_results": True,  # 自动消费结果集，防止未读结果
            "connection_timeout": pool_config["connection_timeout"]
        }

        # 构建初始化命令
        init_commands = []
        
        # 设置 max_allowed_packet (16MB) 需要root用户才行
        # init_commands.append("SET GLOBAL max_allowed_packet = 16777216")
        
        # 只有当数据库名不为空时才添加 USE 命令
        if db_name:
            pool_kwargs["database"] = db_name
            init_commands.append(f"USE {db_name}")
        
        # 将所有初始化命令用分号连接
        if init_commands:
            pool_kwargs["init_command"] = "; ".join(init_commands)

        pool = mysql.connector.pooling.MySQLConnectionPool(**pool_kwargs)
        logger.info(f"MySQL connection pool '{pool_name}' for database '{db_name or 'none'}' initialized successfully")
        return pool
    except Error as e:
        logger.exception(f"Failed to create MySQL connection pool '{pool_config.get('pool_name', 'unknown')}' for database '{pool_config.get('database', 'unknown')}': {e}")
        raise e

# 初始化 ChatBI 数据库连接 - 统一使用MySQL
chatbi_pool_config = {
    "pool_name": CHATBI_POOL_NAME,
    "pool_size": CHATBI_POOL_SIZE,
    "host": CHATBI_DB_HOST,
    "port": CHATBI_DB_PORT,
    "user": CHATBI_DB_USER,
    "password": CHATBI_DB_PASSWORD,
    "database": CHATBI_DB_NAME,
    "connection_timeout": CHATBI_CONNECT_TIMEOUT
}
chatbi_db_pool = _initialize_pool(chatbi_pool_config)
logger.info(f"ChatBI database configured to use MySQL: {CHATBI_DB_HOST}:{CHATBI_DB_PORT}/{CHATBI_DB_NAME}")

# 初始化 Business 连接池
business_pool_config = {
    "pool_name": BUSINESS_POOL_NAME,
    "pool_size": BUSINESS_POOL_SIZE,
    "host": BUSINESS_DB_HOST,
    "port": BUSINESS_DB_PORT,
    "user": BUSINESS_DB_USER,
    "password": BUSINESS_DB_PASSWORD,
    "database": BUSINESS_DB_NAME,
    "connection_timeout": BUSINESS_CONNECT_TIMEOUT
}
business_db_pool = _initialize_pool(business_pool_config)

# 初始化 Logical DW 连接池
logical_dw_pool_config = {
    "pool_name": LOGICAL_DW_POOL_NAME,
    "pool_size": LOGICAL_DW_POOL_SIZE,
    "host": LOGICAL_DW_DB_HOST,
    "port": LOGICAL_DW_DB_PORT,
    "user": LOGICAL_DW_DB_USER,
    "password": LOGICAL_DW_DB_PASSWORD,
    "database": LOGICAL_DW_DB_NAME,
    "connection_timeout": LOGICAL_DW_CONNECT_TIMEOUT
}
logical_dw_db_pool = _initialize_pool(logical_dw_pool_config)




# --- 数据库连接管理 ---

def _ensure_database_context(conn: pooling.PooledMySQLConnection, db_name: str, pool_name: str):
    """
    确保数据库连接具有正确的数据库上下文。

    Args:
        conn: 数据库连接对象
        db_name: 数据库名称（如果为空则跳过检查）
        pool_name: 连接池名称（用于日志）

    Raises:
        Error: 如果无法设置数据库上下文
    """
    # 如果数据库名为空，跳过数据库上下文检查
    if not db_name:
        logger.debug(f"No database specified for pool '{pool_name}', skipping database context check")
        return

    cursor = None
    try:
        # 使用buffered游标避免"Unread result found"错误
        cursor = conn.cursor(buffered=True)

        # 首先检查当前数据库上下文
        cursor.execute("SELECT DATABASE()")
        current_db = cursor.fetchone()
        current_db_name = current_db[0] if current_db and current_db[0] else None

        # 如果当前数据库上下文正确，直接返回
        if current_db_name == db_name:
            logger.debug(f"Database context already set to '{db_name}' for pool '{pool_name}'")
            return

        # 设置数据库上下文
        use_db_sql = f"USE `{db_name}`"
        logger.warning(f"连接没有指定数据库上下文，正在设置: {use_db_sql}")
        cursor.execute(use_db_sql)

    except Error as e:
        logger.exception(f"Failed to set database context to '{db_name}': {e}")
        raise e
    finally:
        # 确保游标被正确关闭
        if cursor:
            try:
                cursor.close()
            except Error as close_err:
                logger.warning(f"Failed to close cursor in _ensure_database_context: {close_err}")

def get_db_connection(database: Union[Database, str] = Database.CHATBI) -> pooling.PooledMySQLConnection:
    """
    获取数据库连接。

    Args:
        database (Union[Database, str]): 要连接的数据库。可以是 Database.CHATBI、Database.BUSINESS 或 Database.LOGICAL_DW。

    Returns:
        MySQL连接池连接。

    Raises:
        Error: 如果相应的连接池未初始化或无法获取连接。
    """
    # 记录要连接的数据库
    db_enum_name = str(database) # e.g., "Database.CHATBI"
    logger.debug(f"Getting connection for {db_enum_name.upper()} database")

    # 根据数据库参数确定使用哪个池和配置
    if database == Database.BUSINESS:
        current_pool = business_db_pool
        pool_name = BUSINESS_POOL_NAME
        db_name = BUSINESS_DB_NAME
        pool_get_timeout = BUSINESS_POOL_GET_TIMEOUT
    elif database == Database.LOGICAL_DW:
        current_pool = logical_dw_db_pool
        pool_name = LOGICAL_DW_POOL_NAME
        db_name = LOGICAL_DW_DB_NAME
        pool_get_timeout = LOGICAL_DW_POOL_GET_TIMEOUT
    else:  # 默认为 chatbi (MySQL)
        current_pool = chatbi_db_pool
        pool_name = CHATBI_POOL_NAME
        db_name = CHATBI_DB_NAME # 实际数据库名称
        pool_get_timeout = CHATBI_POOL_GET_TIMEOUT

    # 检查池是否已成功初始化
    if current_pool is None:
        logger.exception(f"Connection pool '{pool_name}' for {database} is not initialized.")
        raise Error(f"Connection pool '{pool_name}' for {database} is not available.")

    conn = None
    try:
        logger.debug(f"Attempting to get connection from pool '{pool_name}' with timeout {pool_get_timeout}s...")

        # 实现带超时的连接获取
        start_time = time.time()
        retry_interval = 0.1  # 每次重试间隔100ms

        while True:
            try:
                # 尝试获取连接（非阻塞）
                conn = current_pool.get_connection()
                break  # 成功获取连接，跳出循环
            except Error as pool_err:
                # 检查是否是连接池耗尽错误
                if "pool exhausted" in str(pool_err).lower():
                    elapsed_time = time.time() - start_time
                    if elapsed_time >= pool_get_timeout:
                        logger.exception(f"Timeout waiting for connection from pool '{pool_name}' after {pool_get_timeout}s")
                        raise Error(f"Timeout waiting for connection from pool '{pool_name}' after {pool_get_timeout}s") from pool_err

                    # 等待一小段时间后重试
                    logger.warning(f"Pool '{pool_name}' exhausted, retrying in {retry_interval}s... (elapsed: {elapsed_time:.1f}s)")
                    time.sleep(retry_interval)
                    continue
                else:
                    # 其他类型的错误，直接抛出
                    raise pool_err

        if conn and conn.is_connected():
            logger.debug(f"Successfully obtained connection from {database} pool (pool '{pool_name}')")

            _ensure_database_context(conn, db_name, pool_name)
            return conn
        else:
            logger.exception(f"Connection obtained from {database} pool (pool '{pool_name}') is invalid or failed. Connection object: {conn}")
            raise Error(f"Unable to get valid connection from {database} pool (pool '{pool_name}')")
    except Error as e:
        logger.exception(f"Failed to get connection from {database} pool (pool '{pool_name}'): {e}")
        # 如果获取连接时出错，确保 conn 不会泄露（尽管 get_connection 失败时通常不会返回 conn）
        if conn:
             conn.close() # 尝试关闭
        raise

def execute_db_query(sql: str, params: tuple = None, fetch: str = None, commit: bool = False,
                    connection: Optional[pooling.PooledMySQLConnection] = None, database: Union[Database, str] = Database.CHATBI) -> Any:
    """
    辅助函数，用于执行数据库查询或命令，处理连接获取、游标管理和错误。

    Args:
        sql (str): 要执行的 SQL 语句。
        params (tuple, optional): SQL 语句的参数。
        fetch (str, optional): 如何获取结果 ('one', 'all', 'count', None)。'count' 用于获取 rowcount。
        commit (bool): 执行后是否提交事务（仅在非自动提交模式或显式事务中有意义）。
        connection (PooledMySQLConnection, optional): 如果在事务中，传递现有连接。
        database (Union[Database, str], optional): 要连接的数据库。可以是 Database.CHATBI 或 Database.BUSINESS。默认为 Database.CHATBI。

    Returns:
        Any: 查询结果或受影响的行数，具体取决于 fetch 参数。

    Raises:
        Error: 如果数据库操作失败。
    """
    conn = None
    cursor = None
    is_external_conn = connection is not None
    
    # logger.info(f"Executing SQL: {sql} | Params: {params}, database: {database}")

    try:
        # 如果没有提供外部连接，则获取一个新连接
        conn = connection if is_external_conn else get_db_connection(database)
        
        # MySQL 处理逻辑
        # 对于外部连接，需要确保数据库上下文正确
        if is_external_conn:
            if database == Database.CHATBI:
                target_db_name = CHATBI_DB_NAME
                pool_name = CHATBI_POOL_NAME
            elif database == Database.BUSINESS:
                target_db_name = BUSINESS_DB_NAME
                pool_name = BUSINESS_POOL_NAME
            elif database == Database.LOGICAL_DW:
                target_db_name = LOGICAL_DW_DB_NAME
                pool_name = LOGICAL_DW_POOL_NAME
            else:
                target_db_name = CHATBI_DB_NAME
                pool_name = CHATBI_POOL_NAME
            _ensure_database_context(conn, target_db_name, pool_name)

        # 根据 fetch 参数决定是否使用字典游标，启用buffered模式
        use_dict_cursor = fetch in ['one', 'all']
        cursor = conn.cursor(dictionary=use_dict_cursor, buffered=True)

        cursor.execute(sql, params)

        result = None
        if fetch == 'one':
            result = cursor.fetchone()
        elif fetch == 'all':
            result = cursor.fetchall()
        elif fetch == 'count':
            result = cursor.rowcount
        elif fetch == 'lastrowid':
            result = cursor.lastrowid
        else:
            # fetch=None时，仍需要消费结果集以避免"Unread result found"错误
            try:
                while cursor.fetchone() is not None:
                    pass
            except Error:
                # 对于非SELECT语句，fetchone()可能会抛出异常，这是正常的
                pass

        # MySQL 提交逻辑
        if commit and not conn.autocommit:
            logger.debug("Committing transaction...")
            conn.commit()

        return result

    except Error as e:
        logger.exception(f"Database operation failed SQL: {sql} | Params: {params} | Error: {e}")
        # 如果是外部连接（事务），不回滚，让调用者处理
        if conn and not is_external_conn and not conn.autocommit:
            try:
                logger.warning("Operation failed, rolling back MySQL transaction...")
                conn.rollback()
            except Error as rb_err:
                logger.exception(f"Failed to roll back transaction: {rb_err}")
        raise  # 重新引发异常
    finally:
        if cursor:
            cursor.close()
        # 如果连接是内部获取的，则关闭它
        if conn and not is_external_conn:
            conn.close()
            logger.debug("Database connection returned to pool")

# --- 数据库初始化 ---

def initialize_db():
    """
    初始化数据库连接池，验证数据库连接可用性。
    注意：表结构初始化请使用 init.sql 文件。
    """
    # 检查 ChatBI 数据库连接池是否可用
    if chatbi_db_pool:
        try:
            conn = get_db_connection(Database.CHATBI)
            conn.close()
            logger.info("Successfully connected to ChatBI database via pool")
        except Error as e:
            logger.exception(f"Failed to get connection from ChatBI database pool: {e}")
    else:
        logger.warning("ChatBI database pool is not initialized.")

    # 检查 Business 数据库连接池是否可用
    if business_db_pool:
        try:
            conn = get_db_connection(Database.BUSINESS)
            conn.close()
            logger.info("Successfully connected to business database via pool")
        except Error as e:
            logger.exception(f"Failed to get connection from business database pool: {e}")
    else:
        logger.warning("Business database pool is not initialized.")

    # 检查 Logical DW 数据库连接池是否可用
    if logical_dw_db_pool:
        try:
            conn = get_db_connection(Database.LOGICAL_DW)
            conn.close()
            logger.info("Successfully connected to logical DW database via pool")
        except Error as e:
            logger.exception(f"Failed to get connection from logical DW database pool: {e}")
    else:
        logger.warning("Logical DW database pool is not initialized.")


# 注意：数据库表结构初始化已移至 init.sql 文件
# 该模块专注于连接管理，不再包含建表逻辑
